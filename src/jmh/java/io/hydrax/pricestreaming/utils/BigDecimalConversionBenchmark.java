package io.hydrax.pricestreaming.utils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;

/**
 * JMH Benchmark for BigDecimal conversion performance optimization
 * This benchmark compares different approaches to converting 128-bit integers to BigDecimal
 */
@BenchmarkMode(Mode.AverageTime)
@OutputTimeUnit(TimeUnit.NANOSECONDS)
@State(Scope.Benchmark)
@Fork(value = 1, jvmArgs = {"-Xms1G", "-Xmx1G"})
@Warmup(iterations = 2, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 3, time = 1, timeUnit = TimeUnit.SECONDS)
public class BigDecimalConversionBenchmark {

    // Constants
    private static final int SCALE = 18;
    private static final BigDecimal BD_TEN_POWER_OF_SCALE = BigDecimal.TEN.pow(SCALE);
    private static final BigDecimal BD_ZERO = BigDecimal.ZERO;
    private static final BigInteger BI_2_POW_64 = BigInteger.valueOf(2).pow(64);
    
    // Cache for optimized version
    private static final Map<Long, BigDecimal> CACHE = new ConcurrentHashMap<>();
    private static final int MAX_CACHE_SIZE = 1000;
    private static volatile int cacheSize = 0;

    @State(Scope.Benchmark)
    public static class BenchmarkState {
        // Test values
        public long zeroHigh = 0L;
        public long zeroLow = 0L;
        
        public long smallHigh = 0L;
        public long smallLow = 12345L;
        
        public long largeHigh = 123456789L;
        public long largeLow = 987654321L;
        
        public long commonHigh = 0L;
        public long commonLow = 1000000L; // Common value for cache testing
        
        @Setup(Level.Iteration)
        public void clearCache() {
            CACHE.clear();
            cacheSize = 0;
        }
    }

    @Benchmark
    public void benchmarkOriginalImplementation(BenchmarkState state, Blackhole bh) {
        BigDecimal result = toBigDecimalOriginal(state.smallHigh, state.smallLow);
        bh.consume(result);
    }

    @Benchmark
    public void benchmarkOptimizedImplementation(BenchmarkState state, Blackhole bh) {
        BigDecimal result = toBigDecimalOptimized(state.smallHigh, state.smallLow);
        bh.consume(result);
    }

    @Benchmark
    public void benchmarkZeroValueOriginal(BenchmarkState state, Blackhole bh) {
        BigDecimal result = toBigDecimalOriginal(state.zeroHigh, state.zeroLow);
        bh.consume(result);
    }

    @Benchmark
    public void benchmarkZeroValueOptimized(BenchmarkState state, Blackhole bh) {
        BigDecimal result = toBigDecimalOptimized(state.zeroHigh, state.zeroLow);
        bh.consume(result);
    }

    @Benchmark
    public void benchmarkLargeValueOriginal(BenchmarkState state, Blackhole bh) {
        BigDecimal result = toBigDecimalOriginal(state.largeHigh, state.largeLow);
        bh.consume(result);
    }

    @Benchmark
    public void benchmarkLargeValueOptimized(BenchmarkState state, Blackhole bh) {
        BigDecimal result = toBigDecimalOptimized(state.largeHigh, state.largeLow);
        bh.consume(result);
    }

    @Benchmark
    public void benchmarkCacheHitScenario(BenchmarkState state, Blackhole bh) {
        // First call - cache miss
        BigDecimal result1 = toBigDecimalOptimized(state.commonHigh, state.commonLow);
        bh.consume(result1);
        
        // Second call - cache hit
        BigDecimal result2 = toBigDecimalOptimized(state.commonHigh, state.commonLow);
        bh.consume(result2);
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OutputTimeUnit(TimeUnit.SECONDS)
    public void throughputOriginal(BenchmarkState state, Blackhole bh) {
        BigDecimal result = toBigDecimalOriginal(state.smallHigh, state.smallLow);
        bh.consume(result);
    }

    @Benchmark
    @BenchmarkMode(Mode.Throughput)
    @OutputTimeUnit(TimeUnit.SECONDS)
    public void throughputOptimized(BenchmarkState state, Blackhole bh) {
        BigDecimal result = toBigDecimalOptimized(state.smallHigh, state.smallLow);
        bh.consume(result);
    }

    /**
     * Original implementation (similar to current UDec128Util.toBigDecimal)
     */
    private static BigDecimal toBigDecimalOriginal(long high, long low) {
        var highBig = new BigInteger(Long.toUnsignedString(high));
        var lowBig = new BigInteger(Long.toUnsignedString(low));
        var bigInteger = highBig.shiftLeft(64).or(lowBig);
        return new BigDecimal(bigInteger).divide(BD_TEN_POWER_OF_SCALE, MathContext.DECIMAL128);
    }

    /**
     * Optimized implementation with caching and fast paths
     */
    private static BigDecimal toBigDecimalOptimized(long high, long low) {
        // Fast path for zero
        if (high == 0 && low == 0) {
            return BD_ZERO;
        }
        
        // Create cache key
        long cacheKey = high ^ (low >>> 32) ^ (low << 32);
        
        // Try cache first
        BigDecimal cached = CACHE.get(cacheKey);
        if (cached != null) {
            return cached;
        }
        
        // Compute the value
        BigDecimal result = computeBigDecimal(high, low);
        
        // Cache the result if cache is not full
        if (cacheSize < MAX_CACHE_SIZE) {
            CACHE.put(cacheKey, result);
            cacheSize++;
        }
        
        return result;
    }

    /**
     * Optimized computation logic
     */
    private static BigDecimal computeBigDecimal(long high, long low) {
        BigInteger bigInteger;
        
        if (high == 0) {
            // Fast path for values that fit in a single long
            bigInteger = createBigIntegerFromUnsignedLong(low);
        } else {
            // Full 128-bit computation
            BigInteger highBig = createBigIntegerFromUnsignedLong(high);
            BigInteger lowBig = createBigIntegerFromUnsignedLong(low);
            bigInteger = highBig.shiftLeft(64).or(lowBig);
        }
        
        return new BigDecimal(bigInteger).divide(BD_TEN_POWER_OF_SCALE, MathContext.DECIMAL128);
    }

    /**
     * Optimized BigInteger creation from unsigned long
     */
    private static BigInteger createBigIntegerFromUnsignedLong(long value) {
        if (value >= 0) {
            return BigInteger.valueOf(value);
        } else {
            // Handle negative values as unsigned
            return BigInteger.valueOf(value & 0x7FFFFFFFFFFFFFFFL)
                .add(BigInteger.valueOf(0x8000000000000000L));
        }
    }

    // Comparison benchmarks
    @Benchmark
    public void benchmarkStringConversion(BenchmarkState state, Blackhole bh) {
        // Test the cost of string conversion (used in original)
        String highStr = Long.toUnsignedString(state.smallHigh);
        String lowStr = Long.toUnsignedString(state.smallLow);
        bh.consume(highStr);
        bh.consume(lowStr);
    }

    @Benchmark
    public void benchmarkDirectBigIntegerCreation(BenchmarkState state, Blackhole bh) {
        // Test direct BigInteger creation (used in optimized)
        BigInteger result = createBigIntegerFromUnsignedLong(state.smallLow);
        bh.consume(result);
    }

    @Benchmark
    public void benchmarkCacheKeyGeneration(BenchmarkState state, Blackhole bh) {
        // Test cache key generation cost
        long cacheKey = state.smallHigh ^ (state.smallLow >>> 32) ^ (state.smallLow << 32);
        bh.consume(cacheKey);
    }
}
