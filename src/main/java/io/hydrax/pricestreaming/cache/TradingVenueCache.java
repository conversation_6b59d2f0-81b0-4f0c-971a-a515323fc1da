package io.hydrax.pricestreaming.cache;

import io.hydrax.pricestreaming.common.OrderType;
import io.hydrax.pricestreaming.common.TimeInForceEnum;
import io.hydrax.pricestreaming.domain.TradingVenueDTO;
import io.hydrax.proto.metwo.match.TickerRoute;
import io.hydrax.proto.metwo.match.TimeInForce;
import io.hydrax.proto.metwo.match.VenueMarketUpdateRequest;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class TradingVenueCache {
  Map<String, TradingVenueDTO> tradingVenues = new ConcurrentHashMap<>();
  Map<String, List<String>> symbolCodes = new ConcurrentHashMap<>();

  // Performance optimization caches
  // Cache for venue codes by timeInForce + orderType + ticker combination
  private final Map<String, List<String>> venueCodesByQuery = new ConcurrentHashMap<>();
  // Cache for venue ticker codes to avoid repeated Set creation
  private final Map<String, Set<String>> venueTickerCodes = new ConcurrentHashMap<>();

  // Advanced optimization: Index structures for faster lookups
  // Index: ticker -> set of venue codes that support this ticker
  private final Map<String, Set<String>> tickerToVenuesIndex = new ConcurrentHashMap<>();
  // Index: orderType -> set of venue codes that support this order type
  private final Map<String, Set<String>> orderTypeToVenuesIndex = new ConcurrentHashMap<>();
  // Index: timeInForce + orderType -> set of venue codes
  private final Map<String, Set<String>> timeInForceOrderTypeToVenuesIndex = new ConcurrentHashMap<>();

  // Performance optimization: Direct venue lookup by composite key (marketCode:venueMarketCode) to avoid expensive stream operations
  private final Map<String, TradingVenueDTO> venueCompositeKeyToVenueMap = new ConcurrentHashMap<>();

  // Cache statistics for monitoring
  private volatile long cacheHits = 0;
  private volatile long cacheMisses = 0;

  public TradingVenueDTO get(String marketCode, String venueMarketCode) {
    return tradingVenues.get(joinKey(marketCode, venueMarketCode));
  }

  public void put(VenueMarketUpdateRequest request) {
    // Generate a TradingVenue instance based on the request
    TradingVenueDTO venue = TradingVenueDTO.from(request);
    this.put(request.getMarketCode(), request.getCode(), venue);
  }

  public void put(String marketCode, String venueMarketCode, TradingVenueDTO venue) {
    // Update the tradingVenues map with the new or updated TradingVenue data
    tradingVenues.put(joinKey(marketCode, venueMarketCode), venue);

    // Clear performance caches for this venue
    clearCachesForVenue(venueMarketCode);

    // Update the symbolCodes cache
    // Remove any existing cache entries related to the current venue
    symbolCodes.entrySet().removeIf(entry -> entry.getKey().startsWith(venueMarketCode + "_"));

    // Add new symbolCodes data to the cache
    venue
        .getTickersRoute()
        .forEach(
            tickerRoute -> {
              String lpTickerName = tickerRoute.getLpTickerName();
              String cacheKey = venueMarketCode + "_" + lpTickerName;

              // Update the cache with the new ticker codes for the given lpTickerName
              symbolCodes.computeIfAbsent(
                  cacheKey,
                  key ->
                      venue.getTickersRoute().stream()
                          .filter(
                              ticker ->
                                  lpTickerName.equals(
                                      ticker.getLpTickerName())) // Filter by lpTickerName
                          .map(TickerRoute::getTickerCode) // Extract tickerCode
                          .toList() // Collect as an immutable list
                  );
            });

    // Pre-compute and cache venue ticker codes for performance
    cacheVenueTickerCodes(venueMarketCode, venue);

    // Update direct venue lookup map for performance
    venueCodeToVenueMap.put(venueMarketCode, venue);

    // Update indexes for faster lookups
    updateIndexes(venueMarketCode, venue);
  }

  public List<TradingVenueDTO> getAll() {
    return List.copyOf(tradingVenues.values());
  }

  public void remove(VenueMarketUpdateRequest request) {
    tradingVenues.remove(joinKey(request.getMarketCode(), request.getCode()));
    // Remove from direct venue lookup map
    venueCodeToVenueMap.remove(request.getCode());
    // Clear performance caches for this venue
    clearCachesForVenue(request.getCode());
  }

  public List<String> selectCodeByTimeInForceAndOrderType(
      String timeInForce, String orderType, String ticker) {
    // Create cache key for this query - optimized string concatenation
    String queryKey = timeInForce + "|" + orderType + "|" + ticker;

    // Check cache first - fast path for cache hits
    List<String> cachedResult = venueCodesByQuery.get(queryKey);
    if (cachedResult != null) {
      cacheHits++;
      return cachedResult;
    }

    // Cache miss - compute result
    cacheMisses++;

    // Convert types once (these methods already use static caches)
    io.hydrax.proto.metwo.match.PsOrderType psOrderType = OrderType.from(orderType);
    TimeInForce timeInForceProto = TimeInForceEnum.from(timeInForce);

    if (psOrderType == null || timeInForceProto == null) {
      if (log.isWarnEnabled()) {
        log.warn("Invalid orderType: {} or timeInForce: {}", orderType, timeInForce);
      }
      return Collections.emptyList();
    }

    // Use index-based optimization for better performance
    List<String> result = computeVenueCodesWithIndexes(psOrderType, timeInForceProto, orderType, ticker);

    // Cache the result
    venueCodesByQuery.put(queryKey, result);

    return result;
  }

  String joinKey(String marketCode, String venueMarketCode) {
    return marketCode + ":" + venueMarketCode;
  }

  public String getLpTickerName(String marketCode, String venueMarketCode, String symbol) {
    return this.get(marketCode, venueMarketCode).getTickersRoute().stream()
        .filter(tickerRoute -> tickerRoute.getTickerCode().equals(symbol))
        .findFirst()
        .orElse(TickerRoute.newBuilder().build())
        .getLpTickerName();
  }

  public List<String> getMarketCodeByVenueCode(String venueCode) {
    return tradingVenues.values().stream()
        .filter(venue -> venue.getCode().equals(venueCode)) // Filter by code
        .map(TradingVenueDTO::getMarketCode) // Get market code
        .toList();
  }

  public String getLpTickerName(String marketCode, String symbol) {
    return tradingVenues.values().stream()
        .filter(venue -> venue.getMarketCode().equals(marketCode))
        .flatMap(v -> v.getTickersRoute().stream())
        .filter(tickerRoute -> tickerRoute.getTickerCode().equals(symbol))
        .findFirst()
        .orElse(TickerRoute.newBuilder().build())
        .getLpTickerName();
  }

  public List<String> getSymbolCodesByVenueMarketAndVenueSymbol(
      String venueMarketCode, String lpTickerName) {
    // Generate the cache key by combining marketCode and lpTickerName.
    String cacheKey = venueMarketCode + "_" + lpTickerName;

    // Retrieve the ticker codes from the cache if available; otherwise, compute and cache the
    // result.
    return symbolCodes.computeIfAbsent(
        cacheKey,
        key ->
            tradingVenues.values().stream()
                .filter(venue -> venueMarketCode.equals(venue.getCode())) // Filter by marketCode.
                .flatMap(
                    venue -> venue.getTickersRoute().stream()) // Flatten the list of ticker routes.
                .filter(
                    ticker ->
                        lpTickerName.equals(ticker.getLpTickerName())) // Filter by lpTickerName.
                .map(TickerRoute::getTickerCode) // Extract the tickerCode.
                .toList() // Collect as an immutable list.
        );
  }

  // Performance optimization helper methods



  /**
   * Optimized venue matching with early exits and cached ticker codes
   */
  private boolean isVenueMatchingOptimized(TradingVenueDTO venue,
                                          io.hydrax.proto.metwo.match.PsOrderType psOrderType,
                                          TimeInForce timeInForceProto,
                                          String orderType,
                                          String ticker) {
    // Early exit: Check ticker first (most selective filter)
    Set<String> tickerCodes = getCachedVenueTickerCodes(venue.getCode(), venue);
    if (!tickerCodes.contains(ticker)) {
      return false;
    }

    // Check if the venue contains the order type
    if (!venue.getOrderTypes().contains(psOrderType)) {
      return false;
    }

    // Check if the venue contains the time in force for this order type
    Set<TimeInForce> timeInForceSet = venue.getTimeInForces().get(orderType);
    return timeInForceSet != null && timeInForceSet.contains(timeInForceProto);
  }

  /**
   * Gets cached venue ticker codes, computing if not present
   */
  private Set<String> getCachedVenueTickerCodes(String venueCode, TradingVenueDTO venue) {
    return venueTickerCodes.computeIfAbsent(venueCode, key -> {
      return Optional.ofNullable(venue.getTickersRoute())
          .orElse(Collections.emptyList())
          .stream()
          .map(TickerRoute::getTickerCode)
          .collect(Collectors.toSet());
    });
  }

  /**
   * Pre-computes and caches venue ticker codes for performance
   */
  private void cacheVenueTickerCodes(String venueCode, TradingVenueDTO venue) {
    Set<String> tickerCodes = Optional.ofNullable(venue.getTickersRoute())
        .orElse(Collections.emptyList())
        .stream()
        .map(TickerRoute::getTickerCode)
        .collect(Collectors.toSet());
    venueTickerCodes.put(venueCode, tickerCodes);
  }

  /**
   * Computes venue codes using index-based optimization with direct venue lookup
   */
  private List<String> computeVenueCodesWithIndexes(io.hydrax.proto.metwo.match.PsOrderType psOrderType,
                                                    TimeInForce timeInForceProto,
                                                    String orderType,
                                                    String ticker) {
    // Use the most specific index first - time in force + order type combination
    String tifOrderTypeKey = orderType + "|" + timeInForceProto.name();
    Set<String> tifOrderTypeVenues = timeInForceOrderTypeToVenuesIndex.get(tifOrderTypeKey);

    if (tifOrderTypeVenues == null || tifOrderTypeVenues.isEmpty()) {
      return Collections.emptyList();
    }

    // Get ticker venues
    Set<String> tickerVenues = tickerToVenuesIndex.get(ticker);
    if (tickerVenues == null || tickerVenues.isEmpty()) {
      return Collections.emptyList();
    }

    // Find intersection without creating intermediate sets - iterate over smaller set
    Set<String> smallerSet = tifOrderTypeVenues.size() <= tickerVenues.size() ? tifOrderTypeVenues : tickerVenues;
    Set<String> largerSet = tifOrderTypeVenues.size() > tickerVenues.size() ? tifOrderTypeVenues : tickerVenues;

    List<String> result = new ArrayList<>();
    for (String venueCode : smallerSet) {
      if (largerSet.contains(venueCode)) {
        // Since we're using the most specific indexes, we can skip the expensive venue matching
        // The indexes already guarantee the venue supports the combination
        result.add(venueCode);
      }
    }

    return result;
  }

  /**
   * Updates all indexes when a venue is added or modified
   * First removes old index entries, then adds new ones
   */
  private void updateIndexes(String venueCode, TradingVenueDTO venue) {
    // First, remove any existing index entries for this venue
    clearVenueFromIndexes(venueCode);

    // Then add new index entries
    addVenueToIndexes(venueCode, venue);
  }

  /**
   * Adds a venue to all relevant indexes
   */
  private void addVenueToIndexes(String venueCode, TradingVenueDTO venue) {
    // Update ticker index
    Set<String> tickerCodes = getCachedVenueTickerCodes(venueCode, venue);
    for (String ticker : tickerCodes) {
      tickerToVenuesIndex.computeIfAbsent(ticker, k -> ConcurrentHashMap.newKeySet()).add(venueCode);
    }

    // Update order type index
    for (var orderType : venue.getOrderTypes()) {
      String orderTypeName = getOrderTypeName(orderType);
      if (orderTypeName != null) {
        orderTypeToVenuesIndex.computeIfAbsent(orderTypeName, k -> ConcurrentHashMap.newKeySet()).add(venueCode);
      }
    }

    // Update time in force + order type index
    for (Map.Entry<String, Set<TimeInForce>> entry : venue.getTimeInForces().entrySet()) {
      String orderType = entry.getKey();
      for (TimeInForce tif : entry.getValue()) {
        String key = orderType + "|" + tif.name();
        timeInForceOrderTypeToVenuesIndex.computeIfAbsent(key, k -> ConcurrentHashMap.newKeySet()).add(venueCode);
      }
    }
  }

  /**
   * Helper method to get order type name from PsOrderType
   */
  private String getOrderTypeName(io.hydrax.proto.metwo.match.PsOrderType orderType) {
    return switch (orderType) {
      case PS_ORDER_TYPE_MARKET -> "market";
      case PS_ORDER_TYPE_LIMIT -> "limit";
      case PS_ORDER_TYPE_STOP -> "stop";
      default -> null;
    };
  }

  /**
   * Clears all performance caches related to a specific venue
   */
  private void clearCachesForVenue(String venueCode) {
    // Clear venue-specific caches
    venueTickerCodes.remove(venueCode);

    // Clear from direct venue lookup map
    venueCodeToVenueMap.remove(venueCode);

    // Clear from indexes
    clearVenueFromIndexes(venueCode);

    // Clear query caches that might be affected by this venue
    venueCodesByQuery.clear(); // Simple approach - clear all query cache
  }

  /**
   * Removes a venue from all indexes and cleans up empty entries
   */
  private void clearVenueFromIndexes(String venueCode) {
    // Remove from ticker index
    tickerToVenuesIndex.values().forEach(venues -> venues.remove(venueCode));

    // Remove from order type index
    orderTypeToVenuesIndex.values().forEach(venues -> venues.remove(venueCode));

    // Remove from time in force + order type index
    timeInForceOrderTypeToVenuesIndex.values().forEach(venues -> venues.remove(venueCode));

    // Clean up empty sets to prevent memory leaks
    cleanupEmptyIndexEntries();
  }

  /**
   * Removes empty sets from indexes to prevent memory leaks
   */
  private void cleanupEmptyIndexEntries() {
    // Clean ticker index
    tickerToVenuesIndex.entrySet().removeIf(entry -> entry.getValue().isEmpty());

    // Clean order type index
    orderTypeToVenuesIndex.entrySet().removeIf(entry -> entry.getValue().isEmpty());

    // Clean time in force + order type index
    timeInForceOrderTypeToVenuesIndex.entrySet().removeIf(entry -> entry.getValue().isEmpty());
  }

  /**
   * Get cache statistics for monitoring
   */
  public String getCacheStats() {
    long total = cacheHits + cacheMisses;
    double hitRate = total > 0 ? (double) cacheHits / total * 100 : 0;
    return String.format("Cache Stats - Hits: %d, Misses: %d, Hit Rate: %.2f%%, " +
        "Query Cache Size: %d, Venue Ticker Cache Size: %d, Venue Lookup Cache Size: %d",
        cacheHits, cacheMisses, hitRate, venueCodesByQuery.size(), venueTickerCodes.size(),
        venueCodeToVenueMap.size());
  }

  /**
   * Rebuilds the venue lookup map from existing trading venues
   * This method can be called to ensure consistency if needed
   */
  public void rebuildVenueLookupMap() {
    venueCodeToVenueMap.clear();
    for (TradingVenueDTO venue : tradingVenues.values()) {
      venueCodeToVenueMap.put(venue.getCode(), venue);
    }
    log.info("Rebuilt venue lookup map with {} entries", venueCodeToVenueMap.size());
  }

  /**
   * Precompute common queries for better performance
   */
  public void precomputeCommonQueries() {
    if (tradingVenues.isEmpty()) {
      return;
    }

    // Common combinations to precompute
    String[] commonOrderTypes = {"limit", "market"};
    String[] commonTimeInForces = {"gtc", "ioc", "fok"};
    Set<String> commonTickers = tickerToVenuesIndex.keySet();

    log.info("Precomputing {} common query combinations",
        commonOrderTypes.length * commonTimeInForces.length * commonTickers.size());

    for (String orderType : commonOrderTypes) {
      for (String timeInForce : commonTimeInForces) {
        for (String ticker : commonTickers) {
          // This will populate the cache
          selectCodeByTimeInForceAndOrderType(timeInForce, orderType, ticker);
        }
      }
    }

    log.info("Precomputation completed. {}", getCacheStats());
  }
}
