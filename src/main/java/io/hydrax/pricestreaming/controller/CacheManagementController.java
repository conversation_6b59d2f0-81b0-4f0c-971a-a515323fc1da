package io.hydrax.pricestreaming.controller;

import io.hydrax.pricestreaming.service.CacheWarmupService;
import jakarta.inject.Inject;
import jakarta.ws.rs.GET;
import jakarta.ws.rs.POST;
import jakarta.ws.rs.Path;
import jakarta.ws.rs.Produces;
import jakarta.ws.rs.QueryParam;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;

/**
 * REST endpoints for cache management and monitoring
 */
@Path("/api/cache")
@Slf4j
public class CacheManagementController {

  private final CacheWarmupService cacheWarmupService;

  @Inject
  public CacheManagementController(CacheWarmupService cacheWarmupService) {
    this.cacheWarmupService = cacheWarmupService;
  }

  /**
   * Get cache statistics
   */
  @GET
  @Path("/stats")
  @Produces(MediaType.APPLICATION_JSON)
  public Response getCacheStats() {
    try {
      String stats = cacheWarmupService.getCacheStatistics();
      return Response.ok(Map.of(
          "status", "success",
          "statistics", stats,
          "timestamp", System.currentTimeMillis()
      )).build();
    } catch (Exception e) {
      log.error("Error getting cache stats", e);
      return Response.serverError()
          .entity(Map.of("status", "error", "message", e.getMessage()))
          .build();
    }
  }

  /**
   * Trigger cache warmup
   */
  @POST
  @Path("/warmup")
  @Produces(MediaType.APPLICATION_JSON)
  public Response warmupCache() {
    try {
      CompletableFuture<Void> warmupFuture = cacheWarmupService.warmupCacheAsync();
      
      return Response.accepted(Map.of(
          "status", "accepted",
          "message", "Cache warmup started asynchronously",
          "timestamp", System.currentTimeMillis()
      )).build();
    } catch (Exception e) {
      log.error("Error starting cache warmup", e);
      return Response.serverError()
          .entity(Map.of("status", "error", "message", e.getMessage()))
          .build();
    }
  }

  /**
   * Trigger targeted cache warmup for specific tickers
   */
  @POST
  @Path("/warmup/targeted")
  @Produces(MediaType.APPLICATION_JSON)
  public Response warmupCacheForTickers(@QueryParam("tickers") String tickersParam) {
    try {
      if (tickersParam == null || tickersParam.trim().isEmpty()) {
        return Response.status(Response.Status.BAD_REQUEST)
            .entity(Map.of("status", "error", "message", "tickers parameter is required"))
            .build();
      }
      
      String[] tickers = tickersParam.split(",");
      for (int i = 0; i < tickers.length; i++) {
        tickers[i] = tickers[i].trim();
      }
      
      CompletableFuture<Void> warmupFuture = cacheWarmupService.warmupForTradingPairs(tickers);
      
      return Response.accepted(Map.of(
          "status", "accepted",
          "message", "Targeted cache warmup started for " + tickers.length + " tickers",
          "tickers", tickers,
          "timestamp", System.currentTimeMillis()
      )).build();
    } catch (Exception e) {
      log.error("Error starting targeted cache warmup", e);
      return Response.serverError()
          .entity(Map.of("status", "error", "message", e.getMessage()))
          .build();
    }
  }

  /**
   * Health check endpoint
   */
  @GET
  @Path("/health")
  @Produces(MediaType.APPLICATION_JSON)
  public Response healthCheck() {
    return Response.ok(Map.of(
        "status", "healthy",
        "service", "cache-management",
        "timestamp", System.currentTimeMillis()
    )).build();
  }
}
