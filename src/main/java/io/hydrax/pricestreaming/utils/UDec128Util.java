package io.hydrax.pricestreaming.utils;

import io.hydrax.proto.metwo.match.UDec128;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.math.RoundingMode;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.StringUtils;

public final class UDec128Util {
  private static final int SIXTY_FOUR_BIT = 64;
  private static final int SCALE = 18;
  private static final BigDecimal BD_TEN_POWER_OF_SCALE = BigDecimal.TEN.pow(SCALE);
  public static final UDec128 ZERO = UDec128.newBuilder().build();

  // Performance optimization: Cache for frequently used values
  private static final Map<Long, BigDecimal> BIGDECIMAL_CACHE = new ConcurrentHashMap<>();
  private static final int MAX_CACHE_SIZE = 10000;
  private static volatile int cacheSize = 0;

  // Pre-computed constants for common values
  private static final BigDecimal BD_ZERO = BigDecimal.ZERO;
  private static final BigInteger BI_ZERO = BigInteger.ZERO;
  private static final BigInteger BI_2_POW_64 = BigInteger.valueOf(2).pow(64);

  // Cache statistics for monitoring
  private static volatile long cacheHits = 0;
  private static volatile long cacheMisses = 0;

  private UDec128Util() {
    throw new AssertionError("No instances for you!");
  }

  public static BigDecimal toBigDecimal(final UDec128 uDec128) {
    return toBigDecimalOptimized(uDec128);
  }

  /**
   * Optimized version of toBigDecimal with caching and performance improvements
   */
  public static BigDecimal toBigDecimalOptimized(final UDec128 uDec128) {
    // Fast path for zero
    if (uDec128.getHigh() == 0 && uDec128.getLow() == 0) {
      return BD_ZERO;
    }

    // Create cache key from high and low values
    long cacheKey = createCacheKey(uDec128.getHigh(), uDec128.getLow());

    // Try cache first
    BigDecimal cached = BIGDECIMAL_CACHE.get(cacheKey);
    if (cached != null) {
      cacheHits++;
      return cached;
    }

    // Cache miss - compute the value
    cacheMisses++;
    BigDecimal result = computeBigDecimal(uDec128);

    // Cache the result if cache is not full
    if (cacheSize < MAX_CACHE_SIZE) {
      BIGDECIMAL_CACHE.put(cacheKey, result);
      cacheSize++;
    }

    return result;
  }

  /**
   * Original computation logic, optimized for performance
   */
  private static BigDecimal computeBigDecimal(final UDec128 uDec128) {
    long high = uDec128.getHigh();
    long low = uDec128.getLow();

    // Optimized: avoid string conversion for small values
    BigInteger bigInteger;
    if (high == 0) {
      // Fast path for values that fit in a single long
      bigInteger = BigInteger.valueOf(Long.divideUnsigned(low, 1)); // Ensure unsigned
      if (low < 0) {
        // Handle unsigned long values
        bigInteger = bigInteger.add(BI_2_POW_64);
      }
    } else {
      // Full 128-bit computation
      BigInteger highBig = createBigIntegerFromUnsignedLong(high);
      BigInteger lowBig = createBigIntegerFromUnsignedLong(low);
      bigInteger = highBig.shiftLeft(SIXTY_FOUR_BIT).or(lowBig);
    }

    return new BigDecimal(bigInteger).divide(BD_TEN_POWER_OF_SCALE, MathContext.DECIMAL128);
  }

  /**
   * Optimized BigInteger creation from unsigned long
   */
  private static BigInteger createBigIntegerFromUnsignedLong(long value) {
    if (value >= 0) {
      return BigInteger.valueOf(value);
    } else {
      // Handle negative values as unsigned
      return BigInteger.valueOf(value & 0x7FFFFFFFFFFFFFFFL).add(BigInteger.valueOf(0x8000000000000000L));
    }
  }

  /**
   * Creates a cache key from high and low values
   */
  private static long createCacheKey(long high, long low) {
    // Simple hash combination - could be improved for better distribution
    return high ^ (low >>> 32) ^ (low << 32);
  }

  public static UDec128 from(final BigDecimal bdValue) {
    return fromBigDecimalToUDec128(null, bdValue);
  }

  public static UDec128 from(UDec128.Builder builder, BigDecimal val) {
    return fromBigDecimalToUDec128(builder, val);
  }

  private static UDec128 fromBigDecimalToUDec128(UDec128.Builder builder, BigDecimal val) {
    if (null == builder) {
      builder = UDec128.newBuilder();
    } else {
      builder.clear();
    }
    var decimal128 = val.multiply(BD_TEN_POWER_OF_SCALE).toBigInteger();
    var low = decimal128.longValue();
    var high = decimal128.shiftRight(SIXTY_FOUR_BIT).longValue();
    return builder.setHigh(high).setLow(low).build();
  }

  public static UDec128 from(final String strVal) {
    if (StringUtils.isBlank(strVal)) {
      return UDec128.newBuilder().build();
    }
    return fromBigDecimalToUDec128(null, new BigDecimal(strVal));
  }

  public static boolean isPositive(String str) {
    if (StringUtils.isBlank(str)) {
      return false;
    }
    try {
      return (new BigDecimal(str)).compareTo(BigDecimal.ZERO) > 0;
    } catch (NumberFormatException e) {
      // silent
      return false;
    }
  }

  /** comparative: a < b */
  public static boolean isLessThan(UDec128 a, UDec128 b) {
    if (a.getHigh() != b.getHigh()) {
      return Long.compareUnsigned(a.getHigh(), b.getHigh()) < 0;
    }
    return Long.compareUnsigned(a.getLow(), b.getLow()) < 0;
  }

  /** comparative: a > b */
  public static boolean isGreaterThan(UDec128 a, UDec128 b) {
    if (a.getHigh() != b.getHigh()) {
      return Long.compareUnsigned(a.getHigh(), b.getHigh()) > 0;
    }
    return Long.compareUnsigned(a.getLow(), b.getLow()) > 0;
  }

  /** comparative: a == b */
  public static boolean isEqual(UDec128 a, UDec128 b) {
    return a.getHigh() == b.getHigh() && a.getLow() == b.getLow();
  }

  public static boolean isGreaterThanOrEqualTo(UDec128 a, UDec128 b) {
    return isGreaterThan(a, b) || isEqual(a, b);
  }

  public static boolean isLessThanOrEqualTo(UDec128 a, UDec128 b) {
    return isLessThan(a, b) || isEqual(a, b);
  }

  /** add: a + b */
  public static UDec128 add(UDec128 a, UDec128 b) {
    long low = a.getLow() + b.getLow();
    long carry = Long.compareUnsigned(low, a.getLow()) < 0 ? 1 : 0;
    long high = a.getHigh() + b.getHigh() + carry;
    return UDec128.newBuilder().setHigh(high).setLow(low).build();
  }

  /** subtract: a - b (unsigned subtraction, make sure a >= b) */
  public static UDec128 subtract(UDec128 a, UDec128 b) {
    if (isLessThan(a, b)) {
      throw new ArithmeticException("Result would be negative in unsigned arithmetic.");
    }
    long borrow = Long.compareUnsigned(a.getLow(), b.getLow()) < 0 ? 1 : 0;
    long low = a.getLow() - b.getLow();
    long high = a.getHigh() - b.getHigh() - borrow;
    return UDec128.newBuilder().setHigh(high).setLow(low).build();
  }

  /** multiply: a * b / 10^18 */
  public static UDec128 multiply(UDec128 a, UDec128 b) {
    BigInteger bigA = toBigInteger(a);
    BigInteger bigB = toBigInteger(b);
    BigInteger result = bigA.multiply(bigB).divide(BD_TEN_POWER_OF_SCALE.toBigIntegerExact());
    return fromBigInteger(result);
  }

  /** divide: (a * 10^18) / b */
  public static UDec128 divide(UDec128 dividend, UDec128 divisor) {
    if (divisor.getHigh() == 0 && divisor.getLow() == 0) {
      throw new ArithmeticException("Division by zero");
    }
    BigInteger bigDividend =
        toBigInteger(dividend).multiply(BD_TEN_POWER_OF_SCALE.toBigIntegerExact());
    BigInteger bigDivisor = toBigInteger(divisor);
    BigInteger result = bigDividend.divide(bigDivisor);
    return fromBigInteger(result);
  }

  /** UDec128 convert BigInteger */
  private static BigInteger toBigInteger(UDec128 uDec) {
    BigInteger high = new BigInteger(Long.toUnsignedString(uDec.getHigh()));
    BigInteger low = new BigInteger(Long.toUnsignedString(uDec.getLow()));
    return high.shiftLeft(SIXTY_FOUR_BIT).or(low);
  }

  /** BigInteger convert UDec128 */
  private static UDec128 fromBigInteger(BigInteger bigInt) {
    BigInteger mask = BigInteger.valueOf(2).pow(64).subtract(BigInteger.ONE);
    long low = bigInt.and(mask).longValue();
    long high = bigInt.shiftRight(64).longValue();
    return UDec128.newBuilder().setHigh(high).setLow(low).build();
  }

  /** check if UDec128 is zero */
  public static boolean isZero(UDec128 value) {
    return value.getHigh() == 0 && value.getLow() == 0;
  }

  public static UDec128 min(UDec128 a, UDec128 b) {
    // Optimized: use direct comparison instead of BigDecimal conversion
    return isLessThanOrEqualTo(a, b) ? a : b;
  }

  public static UDec128 roundDown(UDec128 value, int scale) {
    BigDecimal bdValue = UDec128Util.toBigDecimal(value);
    BigDecimal rounded = bdValue.setScale(scale, RoundingMode.DOWN);
    return UDec128Util.from(rounded);
  }

  // Performance monitoring and cache management methods

  /**
   * Get cache statistics for monitoring
   */
  public static String getCacheStats() {
    long total = cacheHits + cacheMisses;
    double hitRate = total > 0 ? (double) cacheHits / total * 100 : 0;
    return String.format("UDec128Util Cache Stats - Hits: %d, Misses: %d, Hit Rate: %.2f%%, Cache Size: %d/%d",
        cacheHits, cacheMisses, hitRate, cacheSize, MAX_CACHE_SIZE);
  }

  /**
   * Clear the cache (useful for testing or memory management)
   */
  public static void clearCache() {
    BIGDECIMAL_CACHE.clear();
    cacheSize = 0;
    cacheHits = 0;
    cacheMisses = 0;
  }

  /**
   * Get current cache hit rate
   */
  public static double getCacheHitRate() {
    long total = cacheHits + cacheMisses;
    return total > 0 ? (double) cacheHits / total : 0;
  }

  /**
   * Precompute and cache common values for better performance
   */
  public static void warmupCache() {
    // Cache common values like 0, 1, 10, 100, etc.
    long[] commonValues = {0L, 1L, 10L, 100L, 1000L, 10000L, 100000L, 1000000L};

    for (long value : commonValues) {
      UDec128 uDec = UDec128.newBuilder().setLow(value).build();
      toBigDecimal(uDec);

      // Also cache some high values
      uDec = UDec128.newBuilder().setHigh(value).build();
      toBigDecimal(uDec);
    }
  }
}
