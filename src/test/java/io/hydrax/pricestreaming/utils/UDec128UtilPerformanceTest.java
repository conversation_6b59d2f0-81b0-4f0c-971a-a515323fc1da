package io.hydrax.pricestreaming.utils;

import static org.junit.jupiter.api.Assertions.*;

import io.hydrax.proto.metwo.match.UDec128;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Performance test for UDec128Util.toBigDecimal optimization
 */
public class UDec128UtilPerformanceTest {

  private List<UDec128> testValues;
  private static final int TEST_SIZE = 10000;
  private static final int WARMUP_ITERATIONS = 1000;

  @BeforeEach
  void setUp() {
    // Clear cache before each test
    UDec128Util.clearCache();
    
    // Generate test data
    testValues = generateTestValues(TEST_SIZE);
  }

  @Test
  void testToBigDecimalPerformance() {
    // Warmup
    for (int i = 0; i < WARMUP_ITERATIONS; i++) {
      UDec128 value = testValues.get(i % testValues.size());
      UDec128Util.toBigDecimal(value);
    }

    // Clear cache to test cold performance
    UDec128Util.clearCache();

    // Measure cold performance (cache miss)
    long startTime = System.nanoTime();
    for (UDec128 value : testValues) {
      UDec128Util.toBigDecimal(value);
    }
    long coldTime = System.nanoTime() - startTime;

    // Measure warm performance (cache hit)
    startTime = System.nanoTime();
    for (UDec128 value : testValues) {
      UDec128Util.toBigDecimal(value);
    }
    long warmTime = System.nanoTime() - startTime;

    // Print results
    System.out.printf("Performance test results for %d values:%n", TEST_SIZE);
    System.out.printf("Cold performance (cache miss): %d ns (%.2f ns/op)%n", 
        coldTime, (double) coldTime / TEST_SIZE);
    System.out.printf("Warm performance (cache hit): %d ns (%.2f ns/op)%n", 
        warmTime, (double) warmTime / TEST_SIZE);
    System.out.printf("Performance improvement: %.2fx%n", (double) coldTime / warmTime);
    System.out.printf("Cache stats: %s%n", UDec128Util.getCacheStats());

    // Verify cache hit rate is reasonable
    double hitRate = UDec128Util.getCacheHitRate();
    assertTrue(hitRate > 0.5, "Cache hit rate should be > 50%");
    
    // Warm performance should be significantly better
    assertTrue(warmTime < coldTime / 2, "Warm performance should be at least 2x better");
  }

  @Test
  void testZeroValueOptimization() {
    UDec128 zero = UDec128.newBuilder().build();
    
    // Measure zero value performance
    long startTime = System.nanoTime();
    for (int i = 0; i < 10000; i++) {
      BigDecimal result = UDec128Util.toBigDecimal(zero);
      assertEquals(BigDecimal.ZERO, result);
    }
    long zeroTime = System.nanoTime() - startTime;
    
    System.out.printf("Zero value performance: %d ns (%.2f ns/op)%n", 
        zeroTime, (double) zeroTime / 10000);
    
    // Zero should be very fast
    assertTrue(zeroTime / 10000 < 100, "Zero value conversion should be very fast");
  }

  @Test
  void testCacheEffectiveness() {
    // Create some duplicate values
    List<UDec128> duplicateValues = new ArrayList<>();
    UDec128 commonValue = UDec128.newBuilder().setLow(12345L).build();
    
    for (int i = 0; i < 1000; i++) {
      duplicateValues.add(commonValue);
    }
    
    // Clear cache
    UDec128Util.clearCache();
    
    // Process duplicate values
    for (UDec128 value : duplicateValues) {
      UDec128Util.toBigDecimal(value);
    }
    
    // Check cache stats
    String stats = UDec128Util.getCacheStats();
    System.out.printf("Duplicate values test: %s%n", stats);
    
    // Should have high hit rate
    double hitRate = UDec128Util.getCacheHitRate();
    assertTrue(hitRate > 0.99, "Hit rate should be > 99% for duplicate values");
  }

  @Test
  void testMinMethodOptimization() {
    UDec128 a = UDec128.newBuilder().setLow(100L).build();
    UDec128 b = UDec128.newBuilder().setLow(200L).build();
    
    // Clear cache to ensure we're testing the optimized path
    UDec128Util.clearCache();
    
    // Measure min method performance
    long startTime = System.nanoTime();
    for (int i = 0; i < 10000; i++) {
      UDec128 result = UDec128Util.min(a, b);
      assertEquals(a, result);
    }
    long minTime = System.nanoTime() - startTime;
    
    System.out.printf("Min method performance: %d ns (%.2f ns/op)%n", 
        minTime, (double) minTime / 10000);
    
    // Min should be fast since it uses direct comparison
    assertTrue(minTime / 10000 < 500, "Min method should be fast with direct comparison");
  }

  @Test
  void testCacheWarmup() {
    // Clear cache
    UDec128Util.clearCache();
    
    // Warmup cache
    UDec128Util.warmupCache();
    
    // Check that cache has some entries
    String stats = UDec128Util.getCacheStats();
    System.out.printf("After warmup: %s%n", stats);
    
    // Should have some cached values
    assertTrue(UDec128Util.getCacheStats().contains("Cache Size: "), "Cache should have entries after warmup");
  }

  @Test
  void testLargeValuePerformance() {
    // Test with large values that require full 128-bit computation
    List<UDec128> largeValues = new ArrayList<>();
    Random random = new Random(42);
    
    for (int i = 0; i < 1000; i++) {
      long high = random.nextLong();
      long low = random.nextLong();
      largeValues.add(UDec128.newBuilder().setHigh(high).setLow(low).build());
    }
    
    // Clear cache
    UDec128Util.clearCache();
    
    // Measure performance
    long startTime = System.nanoTime();
    for (UDec128 value : largeValues) {
      UDec128Util.toBigDecimal(value);
    }
    long largeValueTime = System.nanoTime() - startTime;
    
    System.out.printf("Large value performance: %d ns (%.2f ns/op)%n", 
        largeValueTime, (double) largeValueTime / largeValues.size());
    
    // Verify results are correct
    for (UDec128 value : largeValues) {
      BigDecimal result = UDec128Util.toBigDecimal(value);
      assertNotNull(result);
      assertTrue(result.compareTo(BigDecimal.ZERO) >= 0);
    }
  }

  private List<UDec128> generateTestValues(int count) {
    List<UDec128> values = new ArrayList<>();
    Random random = new Random(42); // Fixed seed for reproducible tests
    
    // Add some common values
    values.add(UDec128.newBuilder().build()); // Zero
    values.add(UDec128.newBuilder().setLow(1L).build());
    values.add(UDec128.newBuilder().setLow(10L).build());
    values.add(UDec128.newBuilder().setLow(100L).build());
    values.add(UDec128.newBuilder().setLow(1000L).build());
    
    // Add random values
    for (int i = 5; i < count; i++) {
      long high = random.nextBoolean() ? 0 : random.nextLong() & 0x7FFFFFFFFFFFFFFFL;
      long low = random.nextLong();
      values.add(UDec128.newBuilder().setHigh(high).setLow(low).build());
    }
    
    return values;
  }
}
