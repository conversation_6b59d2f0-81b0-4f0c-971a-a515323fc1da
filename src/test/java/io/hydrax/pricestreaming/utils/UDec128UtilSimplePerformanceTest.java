package io.hydrax.pricestreaming.utils;

import static org.junit.jupiter.api.Assertions.*;

import io.hydrax.proto.metwo.match.UDec128;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

/**
 * Simple performance test for UDec128Util.toBigDecimal optimization
 * This test doesn't require JMH and can run with standard JUnit
 */
public class UDec128UtilSimplePerformanceTest {

  private List<UDec128> testValues;
  private static final int TEST_SIZE = 10000;
  private static final int WARMUP_ITERATIONS = 1000;

  @BeforeEach
  void setUp() {
    // Clear cache before each test
    UDec128Util.clearCache();
    
    // Generate test data
    testValues = generateTestValues(TEST_SIZE);
  }

  @Test
  void testToBigDecimalPerformanceComparison() {
    System.out.println("=== UDec128Util.toBigDecimal Performance Test ===");
    
    // Warmup
    for (int i = 0; i < WARMUP_ITERATIONS; i++) {
      UDec128 value = testValues.get(i % testValues.size());
      UDec128Util.toBigDecimal(value);
      toBigDecimalOriginal(value);
    }

    // Clear cache to test cold performance
    UDec128Util.clearCache();

    // Test 1: Original implementation
    long startTime = System.nanoTime();
    for (UDec128 value : testValues) {
      BigDecimal result = toBigDecimalOriginal(value);
      assertNotNull(result);
    }
    long originalTime = System.nanoTime() - startTime;

    // Clear cache for fair comparison
    UDec128Util.clearCache();

    // Test 2: Optimized implementation (cold cache)
    startTime = System.nanoTime();
    for (UDec128 value : testValues) {
      BigDecimal result = UDec128Util.toBigDecimal(value);
      assertNotNull(result);
    }
    long optimizedColdTime = System.nanoTime() - startTime;

    // Test 3: Optimized implementation (warm cache)
    startTime = System.nanoTime();
    for (UDec128 value : testValues) {
      BigDecimal result = UDec128Util.toBigDecimal(value);
      assertNotNull(result);
    }
    long optimizedWarmTime = System.nanoTime() - startTime;

    // Print results
    System.out.printf("Test size: %d values%n", TEST_SIZE);
    System.out.printf("Original implementation: %d ns (%.2f ns/op)%n", 
        originalTime, (double) originalTime / TEST_SIZE);
    System.out.printf("Optimized (cold cache): %d ns (%.2f ns/op)%n", 
        optimizedColdTime, (double) optimizedColdTime / TEST_SIZE);
    System.out.printf("Optimized (warm cache): %d ns (%.2f ns/op)%n", 
        optimizedWarmTime, (double) optimizedWarmTime / TEST_SIZE);
    
    double coldImprovement = (double) originalTime / optimizedColdTime;
    double warmImprovement = (double) originalTime / optimizedWarmTime;
    
    System.out.printf("Performance improvement (cold): %.2fx%n", coldImprovement);
    System.out.printf("Performance improvement (warm): %.2fx%n", warmImprovement);
    System.out.printf("Cache stats: %s%n", UDec128Util.getCacheStats());

    // Verify warm cache is significantly better
    assertTrue(optimizedWarmTime < optimizedColdTime, 
        "Warm cache should be faster than cold cache");
    
    // Verify optimized version is better than original
    assertTrue(optimizedWarmTime < originalTime, 
        "Optimized version should be faster than original");
  }

  @Test
  void testZeroValuePerformance() {
    System.out.println("=== Zero Value Performance Test ===");
    
    UDec128 zero = UDec128.newBuilder().build();
    int iterations = 100000;
    
    // Test original implementation
    long startTime = System.nanoTime();
    for (int i = 0; i < iterations; i++) {
      BigDecimal result = toBigDecimalOriginal(zero);
      assertEquals(BigDecimal.ZERO, result);
    }
    long originalTime = System.nanoTime() - startTime;
    
    // Test optimized implementation
    startTime = System.nanoTime();
    for (int i = 0; i < iterations; i++) {
      BigDecimal result = UDec128Util.toBigDecimal(zero);
      assertEquals(BigDecimal.ZERO, result);
    }
    long optimizedTime = System.nanoTime() - startTime;
    
    System.out.printf("Zero value test (%d iterations):%n", iterations);
    System.out.printf("Original: %d ns (%.2f ns/op)%n", 
        originalTime, (double) originalTime / iterations);
    System.out.printf("Optimized: %d ns (%.2f ns/op)%n", 
        optimizedTime, (double) optimizedTime / iterations);
    System.out.printf("Improvement: %.2fx%n", (double) originalTime / optimizedTime);
    
    // Zero value should be much faster with optimization
    assertTrue(optimizedTime < originalTime / 5, 
        "Zero value optimization should provide significant improvement");
  }

  @Test
  void testDuplicateValuesPerformance() {
    System.out.println("=== Duplicate Values Performance Test ===");
    
    // Create many duplicate values
    UDec128 commonValue = UDec128.newBuilder().setLow(12345L).build();
    List<UDec128> duplicates = new ArrayList<>();
    for (int i = 0; i < 10000; i++) {
      duplicates.add(commonValue);
    }
    
    // Clear cache
    UDec128Util.clearCache();
    
    // Test with duplicates
    long startTime = System.nanoTime();
    for (UDec128 value : duplicates) {
      BigDecimal result = UDec128Util.toBigDecimal(value);
      assertNotNull(result);
    }
    long duplicateTime = System.nanoTime() - startTime;
    
    System.out.printf("Duplicate values test (%d values):%n", duplicates.size());
    System.out.printf("Time: %d ns (%.2f ns/op)%n", 
        duplicateTime, (double) duplicateTime / duplicates.size());
    System.out.printf("Cache stats: %s%n", UDec128Util.getCacheStats());
    
    // Should have very high cache hit rate
    double hitRate = UDec128Util.getCacheHitRate();
    assertTrue(hitRate > 0.99, "Hit rate should be > 99% for duplicate values");
  }

  @Test
  void testCorrectnessComparison() {
    System.out.println("=== Correctness Verification ===");
    
    int correctCount = 0;
    for (UDec128 value : testValues.subList(0, 1000)) {
      BigDecimal original = toBigDecimalOriginal(value);
      BigDecimal optimized = UDec128Util.toBigDecimal(value);
      
      if (original.compareTo(optimized) == 0) {
        correctCount++;
      } else {
        System.err.printf("Mismatch for value high=%d, low=%d: original=%s, optimized=%s%n",
            value.getHigh(), value.getLow(), original, optimized);
      }
    }
    
    System.out.printf("Correctness: %d/%d (%.2f%%)%n", 
        correctCount, 1000, (double) correctCount / 1000 * 100);
    
    assertEquals(1000, correctCount, "All results should match the original implementation");
  }

  /**
   * Original implementation for comparison
   */
  private static BigDecimal toBigDecimalOriginal(final UDec128 uDec128) {
    var high = new BigInteger(Long.toUnsignedString(uDec128.getHigh()));
    var low = new BigInteger(Long.toUnsignedString(uDec128.getLow()));
    var bigInteger = high.shiftLeft(64).or(low);
    return new BigDecimal(bigInteger).divide(
        BigDecimal.TEN.pow(18), 
        MathContext.DECIMAL128
    );
  }

  private List<UDec128> generateTestValues(int count) {
    List<UDec128> values = new ArrayList<>();
    Random random = new Random(42); // Fixed seed for reproducible tests
    
    // Add some common values
    values.add(UDec128.newBuilder().build()); // Zero
    values.add(UDec128.newBuilder().setLow(1L).build());
    values.add(UDec128.newBuilder().setLow(10L).build());
    values.add(UDec128.newBuilder().setLow(100L).build());
    values.add(UDec128.newBuilder().setLow(1000L).build());
    values.add(UDec128.newBuilder().setLow(1000000L).build());
    
    // Add some duplicate values for cache testing
    UDec128 commonValue = UDec128.newBuilder().setLow(12345L).build();
    for (int i = 0; i < 100; i++) {
      values.add(commonValue);
    }
    
    // Add random values
    for (int i = values.size(); i < count; i++) {
      long high = random.nextBoolean() ? 0 : Math.abs(random.nextLong()) % 1000000L;
      long low = Math.abs(random.nextLong());
      values.add(UDec128.newBuilder().setHigh(high).setLow(low).build());
    }
    
    return values;
  }
}
